"use client"

import React from "react"
import { useTranslations } from "next-intl"

import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import ToggleCommentary from "@/components/layouts/ToggleCommentary"
import CountrySelector from "@/components/summary/filters/CountrySelector"
import Filters from "@/components/summary/filters/Filters"
import { SummaryFiltersProvider } from "@/contexts/summary-filters"

const Layout = ({ children }: { children: React.ReactNode }) => {
  const t = useTranslations("summary")

  return (
    <SummaryFiltersProvider>
      <div className="flex flex-wrap items-center justify-between gap-x-2 gap-y-4">
        <div className="flex flex-wrap items-center gap-2">
          <SidebarTrigger className="-ml-1 hidden md:inline-flex" />
          <Separator
            orientation="vertical"
            className="mr-1 hidden data-[orientation=vertical]:h-6 md:block"
          />
          <h1 className="text-2xl font-medium">{t("title")}</h1>
          <CountrySelector />
          <div />
          <ToggleCommentary />
        </div>

        <Filters />
      </div>

      {children}
    </SummaryFiltersProvider>
  )
}

export default Layout
