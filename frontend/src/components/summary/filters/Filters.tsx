"use client"

import { useState } from "react"
import {
  Calendar,
  Check,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { useTranslations } from "next-intl"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useFilters } from "@/services/summary"

export const COMPARISON_OPTIONS = ["lastTwoYears", "noComparison"] as const
export type ComparisonOption = (typeof COMPARISON_OPTIONS)[number]

const Filters = () => {
  const t = useTranslations("summary.filters")
  const {
    selectedYear,
    setSelectedYear,
    selectedComparison,
    setSelectedComparison,
    selectedFilters,
    setSelectedFilters,
    resetFilters,
  } = useSummaryFilters()

  const [selectedPeriodType, setSelectedPeriodType] = useState<
    "Year" | "Half-year" | "Quarter" | "Month"
  >("Year")
  const [selectedPeriod, setSelectedPeriod] = useState<string>("Year")
  const [showFilters, setShowFilters] = useState<boolean>(false)

  const { data: filters } = useFilters()

  const handleFilterChange = (
    category: string,
    option: string,
    checked: boolean
  ) => {
    setSelectedFilters((prev) => {
      const newFilters = { ...prev }
      if (!newFilters[category]) {
        newFilters[category] = []
      }

      if (checked) {
        newFilters[category] = [...newFilters[category], option]
      } else {
        newFilters[category] = newFilters[category].filter(
          (item) => item !== option
        )
      }

      if (newFilters[category].length === 0) {
        delete newFilters[category]
      }

      return newFilters
    })
  }

  const getFilterSummary = () => {
    const filterEntries = Object.entries(selectedFilters)
    if (filterEntries.length === 0) return null

    return filterEntries
      .map(([category, options]) => `[${category}] ${options.join(", ")}`)
      .join(", ")
  }

  return (
    <>
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex flex-wrap items-center gap-x-3 gap-y-2">
          <YearSelection
            selectedYear={selectedYear}
            setSelectedYear={setSelectedYear}
            selectedPeriod={selectedPeriod}
            setSelectedPeriod={setSelectedPeriod}
            selectedPeriodType={selectedPeriodType}
            setSelectedPeriodType={setSelectedPeriodType}
          />

          <p className="text-sm">{t("comparedTo")}</p>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="justify-between">
                {t(`comparison.${selectedComparison}`)}
                <ChevronDown />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent
              className="min-w-36"
              side="bottom"
              align="start"
              sideOffset={4}
            >
              {COMPARISON_OPTIONS.map((option) => (
                <DropdownMenuItem
                  key={option}
                  onClick={() => setSelectedComparison(option)}
                  className="justify-between"
                >
                  {t(`comparison.${option}`)}
                  {selectedComparison === option && <Check />}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="link"
            onClick={() => setShowFilters(!showFilters)}
            className="text-muted-foreground hover:text-foreground h-auto min-w-14 p-0 underline"
            disabled={filters === undefined}
          >
            {showFilters ? t("seeLess") : t("seeMore")}
          </Button>

          <Button
            variant="link"
            className="text-muted-foreground hover:text-foreground h-auto p-0 underline"
            onClick={resetFilters}
          >
            {t("reset")}
          </Button>
        </div>
      </div>

      {filters && showFilters ? (
        <div className="bg-muted -mt-1 flex w-full flex-wrap gap-2 self-start rounded-md p-2">
          <FilterCard
            title={t("segments")}
            name="Segment"
            options={filters.segments}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
          {/* <FilterCard
            title="Sub-Segment"
            options={["IVF", "Obgyn", "Anti-aging", "Breast Surgery"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          /> */}
          <FilterCard
            title={t("clinics")}
            name="Clinic"
            options={filters.clinics}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
          {/* <FilterCard
            title="Doctor"
            options={["JL", "CC", "KL", "CK"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          />
          <FilterCard
            title="Service"
            options={["Drugs", "Procedure", "Consultation", "Consumables"]}
            selectedFilters={selectedFilters}
            handleFilterChange={handleFilterChange}
          /> */}
        </div>
      ) : (
        getFilterSummary() && (
          <div className="-mt-1 w-full text-sm">
            {t("otherFilters")} {getFilterSummary()}
          </div>
        )
      )}
    </>
  )
}

export default Filters

const YearSelection = ({
  selectedYear,
  setSelectedYear,
  selectedPeriod,
  setSelectedPeriod,
  selectedPeriodType,
  setSelectedPeriodType,
}: {
  selectedYear: number
  setSelectedYear: (year: number) => void
  selectedPeriod: string
  setSelectedPeriod: (period: string) => void
  selectedPeriodType: "Year" | "Half-year" | "Quarter" | "Month"
  setSelectedPeriodType: (
    periodType: "Year" | "Half-year" | "Quarter" | "Month"
  ) => void
}) => {
  const t = useTranslations("summary.filters")

  const getTranslatedPeriod = (period: string) => {
    const periodMap: Record<string, string> = {
      Year: t("periods.year"),
      H1: t("periods.h1"),
      H2: t("periods.h2"),
      "Quarter 1": t("periods.quarter1"),
      "Quarter 2": t("periods.quarter2"),
      "Quarter 3": t("periods.quarter3"),
      "Quarter 4": t("periods.quarter4"),
      January: t("months.january"),
      February: t("months.february"),
      March: t("months.march"),
      April: t("months.april"),
      May: t("months.may"),
      June: t("months.june"),
      July: t("months.july"),
      August: t("months.august"),
      September: t("months.september"),
      October: t("months.october"),
      November: t("months.november"),
      December: t("months.december"),
    }
    return periodMap[period] || period
  }

  const getDisplayText = (period: string, year: number) => {
    if (period === "Year") {
      return t("periods.yearFormat", { year })
    }
    return `${getTranslatedPeriod(period)} ${year}`
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="justify-between">
          <Calendar />
          {getDisplayText(selectedPeriod, selectedYear)}
          <ChevronDown />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="flex min-w-(--radix-dropdown-menu-trigger-width) flex-col gap-2 p-4"
        side="bottom"
        align="start"
        sideOffset={4}
      >
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="icon"
            className="size-6"
            onClick={() => setSelectedYear(selectedYear - 1)}
          >
            <ChevronLeft />
          </Button>

          <span className="font-medium">{selectedYear}</span>

          <Button
            variant="ghost"
            size="icon"
            className="size-6"
            onClick={() => setSelectedYear(selectedYear + 1)}
            disabled={selectedYear >= new Date().getFullYear()}
          >
            <ChevronRight />
          </Button>
        </div>

        <Tabs
          className="hidden"
          value={selectedPeriodType}
          onValueChange={(value) => {
            setSelectedPeriodType(value as typeof selectedPeriodType)

            if (value === "Year") setSelectedPeriod("Year")
            else if (value === "Half-year") setSelectedPeriod("H1")
            else if (value === "Quarter") setSelectedPeriod("Quarter 1")
            else if (value === "Month") setSelectedPeriod("January")
          }}
        >
          <TabsList className="w-full">
            {(["Year", "Half-year", "Quarter", "Month"] as const).map(
              (type) => (
                <TabsTrigger key={type} value={type}>
                  {type === "Year"
                    ? t("periods.year")
                    : type === "Half-year"
                      ? t("periods.halfYear")
                      : type === "Quarter"
                        ? t("periods.quarter")
                        : t("periods.month")}
                </TabsTrigger>
              )
            )}
          </TabsList>

          <TabsContent value="Year">
            <Button
              size="sm"
              className="w-full text-xs"
              onClick={() => setSelectedPeriod("Year")}
            >
              {t("fullYear")}
            </Button>
          </TabsContent>

          <TabsContent value="Half-year">
            <div className="grid grid-cols-2 gap-2">
              {["H1", "H2"].map((half) => (
                <Button
                  key={half}
                  variant={selectedPeriod === half ? "default" : "outline"}
                  size="sm"
                  className="text-xs"
                  onClick={() => setSelectedPeriod(half)}
                >
                  {getTranslatedPeriod(half)}
                </Button>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="Quarter">
            <div className="grid grid-cols-2 gap-2">
              {["Quarter 1", "Quarter 2", "Quarter 3", "Quarter 4"].map(
                (quarter) => (
                  <Button
                    key={quarter}
                    variant={selectedPeriod === quarter ? "default" : "outline"}
                    size="sm"
                    className="text-xs"
                    onClick={() => setSelectedPeriod(quarter)}
                  >
                    {getTranslatedPeriod(quarter)}
                  </Button>
                )
              )}
            </div>
          </TabsContent>

          <TabsContent value="Month">
            <div className="grid grid-cols-2 gap-2">
              {[
                "January",
                "February",
                "March",
                "April",
                "May",
                "June",
                "July",
                "August",
                "September",
                "October",
                "November",
                "December",
              ].map((month) => (
                <Button
                  key={month}
                  variant={selectedPeriod === month ? "default" : "outline"}
                  size="sm"
                  className="text-xs"
                  onClick={() => setSelectedPeriod(month)}
                >
                  {getTranslatedPeriod(month)}
                </Button>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

const FilterCard = ({
  title,
  name,
  options,
  selectedFilters,
  handleFilterChange,
}: {
  title: string
  name: string
  options: string[]
  selectedFilters: Record<string, string[]>
  handleFilterChange: (
    category: string,
    option: string,
    checked: boolean
  ) => void
}) => {
  const maxRows = 14
  const columns = Math.ceil(options.length / maxRows)

  return (
    <Card className="min-w-40 rounded-lg py-3 shadow-xs">
      <CardContent className="flex flex-col gap-3 px-3">
        <CardTitle>{title}</CardTitle>

        <div
          className="grid gap-x-6 gap-y-2"
          style={{
            gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`,
          }}
        >
          {options.map((option) => (
            <Label key={option} className="cursor-pointer font-normal">
              <Checkbox
                checked={selectedFilters[name]?.includes(option) || false}
                onCheckedChange={(checked) =>
                  handleFilterChange(name, option, checked as boolean)
                }
              />
              {option}
            </Label>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
