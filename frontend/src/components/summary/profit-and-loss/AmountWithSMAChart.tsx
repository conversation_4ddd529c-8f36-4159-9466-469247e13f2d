"use client"

import React, { useMemo } from "react"
import { useTranslations } from "next-intl"
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ComposedChart,
  DefaultLegendContent,
  DefaultTooltipContent,
  Legend,
  LegendProps,
  Line,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from "recharts"
import {
  NameType,
  Payload,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent"

import { AmountWithSMA } from "@/types/summary"
import { formatAbbreviatedCurrency, formatCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

export const COLORS = ["#90CAF9", "#2196F3", "#0D47A1"]

export const TYPES = ["Month Year", "Year", "Quarter"] as const
export type Type = (typeof TYPES)[number]

export const VIEWS = ["Default", "Side-by-Side"] as const
export type View = (typeof VIEWS)[number]

const AmountWithSMAChart = ({
  title,
  data,
  type = "Month Year",
  view = "Default",
}: {
  title: string
  data: AmountWithSMA[]
  type?: Type
  view?: View
}) => {
  const t = useTranslations("summary.profitAndLoss")
  const normalizedView: View = type === "Year" ? "Default" : view

  const LINES = [
    {
      name: t("sma.threeMonth"),
      value: "sma3",
      color: "var(--color-gray-500)",
    },
    {
      name: t("sma.sixMonth"),
      value: "sma6",
      color: "var(--color-gray-600)",
    },
  ]

  const uniqueYears = useMemo(
    () => Array.from(new Set(data.map((d) => d.year))).sort((a, b) => a - b),
    [data]
  )

  const yearColorMap = useMemo(() => {
    const mapping: Record<number, string> = {}

    uniqueYears.forEach((year, idx) => {
      mapping[year] = COLORS[idx % COLORS.length]
    })

    return mapping
  }, [uniqueYears])

  const percentageLabel = useMemo(() => {
    if (type === "Month Year") return t("changes.mom")
    if (type === "Quarter") return t("changes.qoq")
    return t("changes.yoy")
  }, [type, t])

  const transformedData = useMemo(() => {
    if (normalizedView !== "Side-by-Side") return data

    const grouped: Record<
      string,
      Record<string, number | string | undefined>
    > = {}

    data.forEach((d) => {
      const baseLabel =
        type === "Month Year"
          ? d.name.split(" ")[0]
          : type === "Quarter"
            ? d.name.split(" ")[0]
            : d.name

      if (!grouped[baseLabel]) grouped[baseLabel] = { name: baseLabel }

      grouped[baseLabel][d.year.toString()] = d.amount
      grouped[baseLabel][`${d.year}-percentage`] = d.percentage
    })

    return Object.values(grouped)
  }, [data, type, normalizedView])

  return (
    <div className="flex flex-col">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT / 2}>
        <ComposedChart data={transformedData}>
          <CartesianGrid strokeDasharray="3 3" />

          <XAxis dataKey="name" hide />

          <YAxis
            width={40}
            type="number"
            fontSize={12}
            tickFormatter={(value) => formatAbbreviatedCurrency(value)}
            domain={[
              (dataMin: number) => dataMin * 0.8,
              (dataMax: number) => dataMax * 1.2,
            ]}
          />

          <Tooltip
            wrapperStyle={{ zIndex: 1 }}
            content={
              <TopChartCustomTooltip percentageLabel={percentageLabel} />
            }
          />

          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
            content={
              <TopChartCustomLegend
                years={uniqueYears}
                colorMapping={yearColorMap}
                view={normalizedView}
                type={type}
              />
            }
          />

          {normalizedView === "Side-by-Side" ? (
            uniqueYears.map((year) => (
              <Bar
                key={year}
                dataKey={year.toString()}
                name={year.toString()}
                fill={yearColorMap[year]}
              />
            ))
          ) : (
            <Bar dataKey="amount" name={title}>
              {data.map((entry, index) => (
                <Cell key={index} fill={yearColorMap[entry.year]} />
              ))}
            </Bar>
          )}

          {type === "Month Year" &&
            normalizedView === "Default" &&
            LINES.map((line) => (
              <Line
                key={line.value}
                dataKey={line.value}
                stroke={line.color}
                name={line.name}
                strokeWidth={2}
                dot={false}
              />
            ))}
        </ComposedChart>
      </ResponsiveContainer>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT / 2}>
        <BarChart data={transformedData}>
          <CartesianGrid strokeDasharray="3 3" />

          <XAxis
            dataKey="name"
            angle={-45}
            textAnchor="end"
            interval={0}
            fontSize={12}
            height={40}
            tickFormatter={(value, index) =>
              !(type === "Month Year" && normalizedView === "Default") ||
              index % 3 === 0
                ? value
                : ""
            }
          />

          <YAxis
            fontSize={12}
            width={40}
            tickFormatter={(value) => `${formatAbbreviatedCurrency(value)}%`}
          />

          <Tooltip
            wrapperStyle={{ zIndex: 1 }}
            content={<BottomChartCustomTooltip />}
          />

          {normalizedView === "Side-by-Side" ? (
            uniqueYears.map((year) => (
              <Bar
                key={year}
                dataKey={`${year}-percentage`}
                name={`${year} ${percentageLabel}`}
              >
                {transformedData.map((entry, idx) => {
                  const val = (
                    entry as Record<string, number | string | undefined>
                  )[`${year}-percentage`] as number | undefined

                  return (
                    <Cell
                      key={idx}
                      fill={
                        !!val && val < 0
                          ? "var(--color-red-600)"
                          : "var(--color-green-600)"
                      }
                    />
                  )
                })}
              </Bar>
            ))
          ) : (
            <Bar dataKey="percentage" name={percentageLabel}>
              {data.map((entry, index) => (
                <Cell
                  key={index}
                  fill={
                    (entry.percentage ?? 0) >= 0
                      ? "var(--color-green-600)"
                      : "var(--color-red-600)"
                  }
                />
              ))}
            </Bar>
          )}
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}

export default AmountWithSMAChart

const TopChartCustomLegend = ({
  years,
  colorMapping,
  view,
  type,
  ...props
}: Omit<LegendProps, "ref"> & {
  years: number[]
  colorMapping: Record<number, string>
  view: View
  type: string
}) => {
  const payload = [
    ...years.map((year) => ({
      type: "rect",
      color: colorMapping[year],
      value: year,
    })),
    ...(type === "Month Year" && view === "Default"
      ? LINES.map((line) => ({
          type: "line",
          color: line.color,
          value: line.name,
        }))
      : []),
  ] as LegendProps["payload"]

  return <DefaultLegendContent {...props} payload={payload} />
}

const TopChartCustomTooltip = ({
  percentageLabel,
  ...props
}: TooltipProps<ValueType, NameType> & {
  percentageLabel: string
}) => {
  if (!props.active || !props.payload || props.payload.length === 0) return null

  const rawPayload = props.payload[0].payload

  const formattedBasePayload = props.payload.map((item) => ({
    ...item,
    value: formatAbbreviatedCurrency(item.value as number),
  }))

  const percentageEntries: Payload<ValueType, NameType>[] = []

  if (rawPayload.percentage) {
    percentageEntries.push({
      name: percentageLabel,
      value: `${formatCurrency(rawPayload.percentage)}%`,
      color:
        rawPayload.percentage >= 0
          ? "var(--color-green-600)"
          : "var(--color-red-600)",
    } as Payload<ValueType, NameType>)
  }

  Object.entries(rawPayload).forEach(([key, val]) => {
    if (key.endsWith("-percentage") && typeof val === "number" && val !== 0) {
      const year = key.replace("-percentage", "")
      percentageEntries.push({
        name: `${year} ${percentageLabel}`,
        value: `${formatCurrency(val)}%`,
        color: val >= 0 ? "var(--color-green-600)" : "var(--color-red-600)",
      } as Payload<ValueType, NameType>)
    }
  })

  return (
    <DefaultTooltipContent
      {...props}
      payload={[...formattedBasePayload, ...percentageEntries]}
    />
  )
}

const BottomChartCustomTooltip = (props: TooltipProps<ValueType, NameType>) => {
  if (!props.active || !props.payload || props.payload.length === 0) return null

  const payload = props.payload.map((item) => ({
    ...item,
    value: `${formatCurrency(item.value as number)}%`,
    color:
      (item.value as number) >= 0
        ? "var(--color-green-600)"
        : "var(--color-red-600)",
  })) as Payload<ValueType, NameType>[]

  return <DefaultTooltipContent {...props} payload={payload} />
}
