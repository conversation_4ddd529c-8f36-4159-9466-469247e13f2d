"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { CustomSelect } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import AmountWithSMAChart, {
  Type,
  TYPES,
  View,
  VIEWS,
} from "@/components/summary/profit-and-loss/AmountWithSMAChart"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossEBITDAWithSMA,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields()
const initialRowFields = getPNLInitialRowFields(["ebitda"])

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

const EBITDA = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const [type, setType] = useState<string>(TYPES[0])

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossEBITDAWithSMA({
    ...filters,
    type,
  })
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [view, setView] = useState<string>(VIEWS[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title={t("charts.ebitda")}
      filters={
        <>
          <CustomSelect
            value={view as string}
            setValue={setView}
            options={[...VIEWS]}
            disabled={isLoading}
          />

          <CustomSelect
            value={type as string}
            setValue={setType}
            options={[...TYPES]}
            disabled={isLoading}
          />
        </>
      }
      icons={
        <AdvancedDrillDownModal
          title={t("charts.ebitda")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <AmountWithSMAChart
        title={t("charts.ebitda")}
        data={data}
        type={type as Type}
        view={view as View}
      />
    </Card>
  )
}

export default EBITDA
