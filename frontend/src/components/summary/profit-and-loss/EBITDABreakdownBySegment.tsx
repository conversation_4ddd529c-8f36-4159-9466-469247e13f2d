"use client"

import React from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import SegmentBreakdownChart from "@/components/summary/profit-and-loss/SegmentBreakdownChart"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossEBITDASegmentBreakdown,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields(["year", "segment"])
const initialRowFields = getPNLInitialRowFields(["ebitda"])

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

const EBITDABreakdownBySegment = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossEBITDASegmentBreakdown(filters)
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title={t("charts.ebitdaBreakdownBySegment")}
      icons={
        <AdvancedDrillDownModal
          title={t("charts.ebitda")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <SegmentBreakdownChart data={data} />
    </Card>
  )
}

export default EBITDABreakdownBySegment
