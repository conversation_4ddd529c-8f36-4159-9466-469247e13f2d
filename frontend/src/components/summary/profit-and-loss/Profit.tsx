"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { CustomSelect } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import AmountWithSMAChart, {
  Type,
  TYPES,
  View,
  VIEWS,
} from "@/components/summary/profit-and-loss/AmountWithSMAChart"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossProfitWithSMA,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields()
const initialRowFields = getPNLInitialRowFields(["net_profit"])

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

export const TABS = ["Net Profit", "Operating Profit"]

const Profit = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const [type, setType] = useState<string>(TYPES[0])
  const [selectedTab, setSelectedTab] = useState<string>(TABS[0])

  // Translation mappings for tabs
  const getTranslatedTab = (tab: string) => {
    const tabMap: Record<string, string> = {
      "Net Profit": t("tabs.netProfit"),
      "Operating Profit": t("tabs.operatingProfit"),
    }
    return tabMap[tab] || tab
  }

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossProfitWithSMA(
    {
      ...filters,
      type,
    },
    selectedTab
  )
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [view, setView] = useState<string>(VIEWS[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title={t("charts.profit")}
      tabs={TABS.map(getTranslatedTab)}
      selectedTab={getTranslatedTab(selectedTab)}
      onTabChange={(tab) => {
        // Find the original tab key from the translated value
        const originalTab =
          TABS.find((t) => getTranslatedTab(t) === tab) || TABS[0]
        setSelectedTab(originalTab)
      }}
      filters={
        <>
          <CustomSelect
            value={view as string}
            setValue={setView}
            options={[...VIEWS]}
            disabled={isLoading}
          />

          <CustomSelect
            value={type as string}
            setValue={setType}
            options={[...TYPES]}
            disabled={isLoading}
          />
        </>
      }
      icons={
        <AdvancedDrillDownModal
          title={t("charts.profit")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <AmountWithSMAChart
        title={getTranslatedTab(selectedTab)}
        data={data}
        type={type as Type}
        view={view as View}
      />
    </Card>
  )
}

export default Profit
