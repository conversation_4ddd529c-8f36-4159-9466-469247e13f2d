"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import { TABS } from "@/components/summary/profit-and-loss/Profit"
import SegmentBreakdownChart from "@/components/summary/profit-and-loss/SegmentBreakdownChart"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossProfitSegmentBreakdown,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields(["year", "segment"])
const initialRowFields = getPNLInitialRowFields(["net_profit"])

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

const ProfitBreakdownBySegment = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const [selectedTab, setSelectedTab] = useState<string>(TABS[0])

  // Translation mappings for tabs
  const getTranslatedTab = (tab: string) => {
    const tabMap: Record<string, string> = {
      "Net Profit": t("tabs.netProfit"),
      "Operating Profit": t("tabs.operatingProfit"),
    }
    return tabMap[tab] || tab
  }

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossProfitSegmentBreakdown(filters, selectedTab)
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title={t("charts.profitBreakdownBySegment")}
      tabs={TABS.map(getTranslatedTab)}
      selectedTab={getTranslatedTab(selectedTab)}
      onTabChange={(tab) => {
        // Find the original tab key from the translated value
        const originalTab =
          TABS.find((t) => getTranslatedTab(t) === tab) || TABS[0]
        setSelectedTab(originalTab)
      }}
      icons={
        <AdvancedDrillDownModal
          title={t("charts.profit")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <SegmentBreakdownChart data={data} />
    </Card>
  )
}

export default ProfitBreakdownBySegment
