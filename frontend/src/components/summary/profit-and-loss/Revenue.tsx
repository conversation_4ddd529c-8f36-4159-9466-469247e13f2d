"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { CustomSelect } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import AmountWithSMAChart, {
  Type,
  TYPES,
  View,
  VIEWS,
} from "@/components/summary/profit-and-loss/AmountWithSMAChart"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossRevenueWithSMA,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields()
const initialRowFields = getPNLInitialRowFields()

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

const Revenue = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const [type, setType] = useState<string>(TYPES[0])

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossRevenueWithSMA({
    ...filters,
    type,
  })
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [view, setView] = useState<string>(VIEWS[0])

  const [withCommentary] = useWithCommentary()

  // Translation mappings
  const getTranslatedType = (type: string) => {
    const typeMap: Record<string, string> = {
      "Month Year": t("types.monthYear"),
      Year: t("types.year"),
      Quarter: t("types.quarter"),
    }
    return typeMap[type] || type
  }

  const getTranslatedView = (view: string) => {
    const viewMap: Record<string, string> = {
      Default: t("views.default"),
      "Side-by-Side": t("views.sideBySide"),
    }
    return viewMap[view] || view
  }

  return (
    <Card
      title={t("charts.revenue")}
      filters={
        <>
          <CustomSelect
            value={view as string}
            setValue={setView}
            options={[...VIEWS]}
            disabled={isLoading}
          />

          <CustomSelect
            value={type as string}
            setValue={setType}
            options={[...TYPES]}
            disabled={isLoading}
          />
        </>
      }
      icons={
        <AdvancedDrillDownModal
          title={t("charts.revenue")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <AmountWithSMAChart
        title={t("charts.revenue")}
        data={data}
        type={type as Type}
        view={view as View}
      />
    </Card>
  )
}

export default Revenue
