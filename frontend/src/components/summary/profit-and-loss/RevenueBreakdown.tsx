"use client"

import React from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import RevenueBreakdownChart from "@/components/summary/profit-and-loss/RevenueBreakdownChart"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossRevenueBreakdown,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields(["year", "segment"])
const initialRowFields = getPNLInitialRowFields()

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

const RevenueBreakdown = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossRevenueBreakdown({
    type: "by-segment",
    filters,
  })
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title={t("charts.revenueBreakdown")}
      icons={
        <AdvancedDrillDownModal
          title={t("charts.revenueBreakdown")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <RevenueBreakdownChart data={data} />
    </Card>
  )
}

export default RevenueBreakdown
