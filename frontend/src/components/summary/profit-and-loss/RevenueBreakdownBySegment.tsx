"use client"

import React from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import SegmentBreakdownChart from "@/components/summary/profit-and-loss/SegmentBreakdownChart"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossRevenueSegmentBreakdown,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields(["year", "segment"])
const initialRowFields = getPNLInitialRowFields()

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

const RevenueBreakdownBySegment = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossRevenueSegmentBreakdown(filters)
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title={t("charts.revenueBreakdownBySegment")}
      icons={
        <AdvancedDrillDownModal
          title={t("charts.revenueBreakdown")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <SegmentBreakdownChart data={data} />
    </Card>
  )
}

export default RevenueBreakdownBySegment
