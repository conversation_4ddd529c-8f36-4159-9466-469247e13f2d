"use client"

import React, { useState } from "react"
import { useTranslations } from "next-intl"

import { DrillDownConfiguration } from "@/types/drill-down"
import AdvancedDrillDownModal from "@/components/advanced/AdvancedDrillDownModal"
import { Card } from "@/components/CardComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import {
  availableFields,
  getPNLInitialColumnFields,
  getPNLInitialRowFields,
} from "@/components/summary/profit-and-loss/constants"
import LineOnTopBarChart from "@/components/summary/profit-and-loss/LineOnTopBarChart"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossDrillDown,
  useProfitAndLossRevenueEBITDAProfitMargin,
} from "@/services/summary"

const initialColumnFields = getPNLInitialColumnFields()
const initialRowFields = getPNLInitialRowFields([
  "revenue",
  "ebitda",
  "net_profit",
])

const initialDrillDownConfig: DrillDownConfiguration = {
  filters: [],
  columns: initialColumnFields,
  rows: initialRowFields,
}

const TABS = ["Pre-IFRS", "Post-IFRS"]

const RevenueEBITDAProfitMargin = () => {
  const t = useTranslations("summary.profitAndLoss")
  const { filters } = useSummaryFilters()

  const [selectedTab, setSelectedTab] = useState<string>(TABS[0])

  // Translation mappings for tabs
  const getTranslatedTab = (tab: string) => {
    const tabMap: Record<string, string> = {
      "Pre-IFRS": t("tabs.preIFRS"),
      "Post-IFRS": t("tabs.postIFRS"),
    }
    return tabMap[tab] || tab
  }

  const {
    data: { chart_1_data, chart_2_data, commentary },
    isLoading,
  } = useProfitAndLossRevenueEBITDAProfitMargin(filters, selectedTab)
  const { data: drillDownData, isLoading: isDrillDownLoading } =
    useProfitAndLossDrillDown(filters)

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title={t("charts.revenueVsEbitdaVsProfitMargin")}
      tabs={TABS.map(getTranslatedTab)}
      selectedTab={getTranslatedTab(selectedTab)}
      onTabChange={(tab) => {
        // Find the original tab key from the translated value
        const originalTab =
          TABS.find((t) => getTranslatedTab(t) === tab) || TABS[0]
        setSelectedTab(originalTab)
      }}
      icons={
        <AdvancedDrillDownModal
          title={t("charts.revenueVsEbitdaVsProfitMargin")}
          data={drillDownData}
          availableFields={availableFields}
          initialDrillDownConfig={initialDrillDownConfig}
          isLoading={isDrillDownLoading}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <LineOnTopBarChart
        chart1Data={chart_1_data}
        chart2Data={chart_2_data}
        barTitle={t("charts.revenue")}
        lineTitle={t("charts.ebitda")}
        percentageTitle={t("charts.profitMargin")}
      />
    </Card>
  )
}

export default RevenueEBITDAProfitMargin
