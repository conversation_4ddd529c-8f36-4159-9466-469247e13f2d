import { DrillDownField } from "@/types/drill-down"

// Base field definitions without translations
const baseFields = [
  {
    id: "period",
    dataType: "string",
    iconType: "calendar",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "year",
    dataType: "number",
    iconType: "calendar",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "month",
    dataType: "number",
    iconType: "calendar",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "clinic",
    dataType: "string",
    iconType: "tag",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "segment",
    dataType: "string",
    iconType: "tag",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "account",
    dataType: "string",
    iconType: "tag",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "revenue",
    dataType: "number",
    iconType: "hash",
    role: "measures",
    attribute: "continuous",
    aggregation: "sum",
  },
  {
    id: "ebitda",
    dataType: "number",
    iconType: "hash",
    role: "measures",
    attribute: "continuous",
    aggregation: "sum",
  },
  {
    id: "net_profit",
    dataType: "number",
    iconType: "hash",
    role: "measures",
    attribute: "continuous",
    aggregation: "sum",
  },
] as const

// Function to get translated fields
export const getAvailableFields = (
  t: (key: string) => string
): DrillDownField[] =>
  baseFields.map((field) => ({
    ...field,
    name: t(`fields.${field.id}`),
  }))

// Default export for backward compatibility
export const availableFields: DrillDownField[] = [
  {
    id: "period",
    name: "Period",
    dataType: "string",
    iconType: "calendar",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "year",
    name: "Year",
    dataType: "number",
    iconType: "calendar",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "month",
    name: "Month",
    dataType: "number",
    iconType: "calendar",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "clinic",
    name: "Clinic",
    dataType: "string",
    iconType: "tag",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "segment",
    name: "Segment",
    dataType: "string",
    iconType: "tag",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "account",
    name: "Account",
    dataType: "string",
    iconType: "tag",
    role: "dimensions",
    attribute: "discrete",
  },
  {
    id: "revenue",
    name: "Revenue",
    dataType: "number",
    iconType: "hash",
    role: "measures",
    attribute: "continuous",
    aggregation: "sum",
  },
  {
    id: "ebitda",
    name: "EBITDA",
    dataType: "number",
    iconType: "hash",
    role: "measures",
    attribute: "continuous",
    aggregation: "sum",
  },
  {
    id: "net_profit",
    name: "Net Profit",
    dataType: "number",
    iconType: "hash",
    role: "measures",
    attribute: "continuous",
    aggregation: "sum",
  },
]

export const getPNLInitialColumnFields = (
  fieldIds: DrillDownField["id"][] = ["year"]
) =>
  availableFields
    .filter((field) => fieldIds.includes(field.id))
    .map((field) => ({ ...field, instanceId: `${field.id}-${Date.now()}` }))
export const getPNLInitialRowFields = (
  fieldIds: DrillDownField["id"][] = ["revenue"]
) =>
  availableFields
    .filter((field) => fieldIds.includes(field.id))
    .map((field) => ({ ...field, instanceId: `${field.id}-${Date.now()}` }))
